import {
  View,
  Text,
  ScrollView,
  Alert,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Button,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import Header from '../../../components/Inventory/Header';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AppButton from '../../../components/Inventory/AppButton';
import {
  AdditionalInfo,
  AltSKU,
  BrandAdd,
  BrandOrSubCategory,
  Brands,
  Department,
  Inventory,
  Inventory_In,
  Inventory_Vendors,
  InventoryVendor,
  PurchaseOrder,
  PurchaseOrderItems,
  SubCategories,
  SubCategoryAdd,
  TagAlong,
  TagAlong_View,
  Unit_Types,
  UpdatePurchaseOrder,
  Vendor,
} from '../../../server/types';
import {
  applyDefaults,
  applyDefaultsAddition,
  applyDefaultsAltSKU,
  applyDefaultsInventoryAdjust,
  applyDefaultsInventoryVendor,
  applyDefaultsInventoryVendorsUpdate,
  applyDefaultsPurchaseOrderItem,
  applyDefaultsUpdatePurchaseOrder,
} from '../../../Validator/Inventory/Barcode';
import {
  createData,
  createInventoryRef,
  findDifferenceCommon,
  formatNumber,
  GetAllItems,
  GetAllItemsWithFilter,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  showAlert,
  showAlertMulti,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import BarocdeSwitch from '../../../components/Inventory/BarocdeSwitch';

import {createItem, deleteItem} from '../../../server/service';
import DataList from '../../../components/Inventory/AppList';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AppFocus from '../../../components/Inventory/AppFocus';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Search from '../../../components/Inventory/Search';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import {Activate_Book} from '../../../Types/Lottery/Lottery_Types';
import {applyDefaultsActivateBook} from '../../../Validator/Lottery/Lottery_Validator';
import {hasPermission} from '../../../utils/permissionHelper';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type BarcodeScreenRouteProp = RouteProp<any, 'Barcode'>;

const BarcodeDetails: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [itemData, setItemData] = useState<Inventory[]>(
    route.params?.ItemData || [],
  );
  const [itemDataFilter, setItemDataFiler] = useState<Inventory[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [tagAlongsItems, setTagAlongsItems] = useState<TagAlong[]>([]);
  const [inventoryVendor, setInventoryVendor] = useState<InventoryVendor[]>(
    route.params?.VENDORITEM || [],
  );
  const [inventoryAdditional, setInventoryAdditional] = useState<
    AdditionalInfo[]
  >(route.params?.ADDITIONAL || []);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalVisibleHistory, setModalVisibleHistory] =
    useState<boolean>(false);
  const [tagAlong, setTagAlong] = useState<boolean>(false);
  const [inserted, setInserted] = useState<boolean>(false);
  const [AltSKUsData, setAltSKUsData] = useState<AltSKU[]>([]);
  const [BookHistory, setBookHistory] = useState<Activate_Book[]>([]);
  const [initAltSKUsData, setInitAltSKUsData] = useState<AltSKU[]>([]);
  const [tagAlongs, setTagAlongs] = useState<TagAlong_View[]>([]);
  const [tagAlongsFilter, setTagAlongsFilter] = useState<TagAlong_View[]>([]);
  const [tagAlongsSelected, setTagSelected] = useState<TagAlong_View[]>([]);
  const [unitTypes, setUnitTypes] = useState<Unit_Types[]>([]);
  const [newSKU, setNewSKU] = useState<string>('');
  const [success, setSuccess] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [showMore, setShowMore] = useState<boolean>(false);

  const [vendorNumber, setVendorNumber] = useState<string>(
    route.params?.Vendor || '',
  );
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [branding, setBranding] = useState<BrandAdd[]>([]);
  const [subCat, setSubCat] = useState<SubCategoryAdd[]>([]);

  const [purchaseOrder, setPurchaseOrder] = useState<PurchaseOrder>(
    route.params?.MainPO || '',
  );

  const [itemNumber, setItemNumber] = useState<string>(
    itemData[0]?.ItemNum || '',
  );

  const [itemName, setItemName] = useState<string>(itemData[0]?.ItemName || '');
  const [itemNameextra, setItemNameextra] = useState<string>(
    itemData[0]?.ItemName_Extra || '',
  );
  const [cost, setCost] = useState<number | string>(itemData[0]?.Cost || '');
  const [price, setPrice] = useState<number | string>(itemData[0]?.Price || '');
  const [rentalPrice, setRentalPrice] = useState<number | string>(
    itemData[0]?.Retail_Price || '',
  );
  const [inStock, setInStock] = useState<number | string>(
    itemData[0]?.In_Stock || '',
  );
  const [department, setDepartment] = useState<string>(
    itemData[0]?.Dept_ID || route.params?.LOTTERYDEPT || '',
  );
  const [tags, setTags] = useState<string>('');
  const [location, setLocation] = useState<string>(itemData[0]?.Location || '');
  const [vendor, setVendor] = useState<string>(
    itemData[0]?.Vendor_Number || route?.params?.VENDOR || '',
  );
  const [vendorpart, setVendorPart] = useState<string>(
    itemData[0]?.Vendor_Part_Num || '',
  );
  const [openSerial, setOpenSerial] = useState<string>('');
  const [reoderquanity, setReoderQuanity] = useState<number | string>(
    itemData[0]?.Reorder_Quantity || '',
  );
  const [reorderlevel, setReorderLevel] = useState<number | string>(
    itemData[0]?.Reorder_Level || '',
  );
  const [reordercost, setReorderCost] = useState<number | string>(
    itemData[0]?.ReOrder_Cost || '',
  );
  const [unitsize, setUnitSize] = useState<number | string>(
    itemData[0]?.Unit_Size || '',
  );
  const [unitType, setUnitType] = useState<string>(
    itemData[0]?.Unit_Type || '',
  );
  const [foodstampable, setfoodstampable] = useState<boolean>(
    itemData[0]?.FoodStampable || false,
  );
  const [tax1, setTax1] = useState<boolean>(itemData[0]?.Tax_1 || false);
  const [tax2, setTax2] = useState<boolean>(itemData[0]?.Tax_2 || false);
  const [tax3, setTax3] = useState<boolean>(itemData[0]?.Tax_3 || false);
  const [tax4, setTax4] = useState<boolean>(itemData[0]?.Tax_4 || false);
  const [tax5, setTax5] = useState<boolean>(itemData[0]?.Tax_5 || false);
  const [tax6, setTax6] = useState<boolean>(itemData[0]?.Tax_6 || false);
  const [checkid1, setCheckid1] = useState<boolean>(
    itemData[0]?.Check_ID || false,
  );
  const [checkid2, setCheckid2] = useState<boolean>(
    itemData[0]?.Check_ID2 || false,
  );
  const [costper, setCostPer] = useState<number | string>(
    inventoryVendor[0]?.CostPer || '',
  );
  const [casecost, setCaseCost] = useState<number | string>(
    inventoryVendor[0]?.Case_Cost || '',
  );
  const [numberincase, setNumberinCase] = useState<number | string>(
    inventoryVendor[0]?.NumPerVenCase || '',
  );
  const [poQuanity, setPoQuanity] = useState<number | string>('');
  const [subcategory, setSubcategory] = useState<string>(
    inventoryAdditional[0]?.SubCategory || '',
  );
  const [brand, setBrand] = useState<string>(
    inventoryAdditional[0]?.Brand || '',
  );

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filter, setFilter] = useState<boolean>(false);
  const [isUpdate, setIsUpdate] = useState<boolean>(false);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [isRestore, setIsRestore] = useState<boolean>(false);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [hook, setHook] = useState<boolean>(false);
  const [lotteryBook, setLotteryBook] = useState<string>('');
  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);
  const [brandorSubCat, SetBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(false);

  const textInputRef = useRef<TextInput>(null);
  const textInputRefOrdering = useRef<TextInput>(null);
  const textInputRefGameNumber = useRef<TextInput>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const [isSameDepartment, setIsSameDepartment] = useState<boolean>(false);
  const [initialState, setInitialState] = useState({
    itemNumber,
    itemName,
    itemNameextra,
    cost,
    price,
    rentalPrice,
    inStock,
    department,
    tags,
    location,
    vendor,
    vendorpart,
    reoderquanity,
    reorderlevel,
    reordercost,
    unitsize,
    unitType,
    foodstampable,
    tax1,
    tax2,
    tax3,
    tax4,
    tax5,
    tax6,
    checkid1,
    checkid2,
    costper,
    casecost,
    numberincase,
    poQuanity,
    subcategory,
    brand,
  });
  const [bookDetails, setBookDetails] = useState<Activate_Book>();
  //console.log('itemsssssss', itemNumber);
  useEffect(() => {
    if (route.params?.BARCODENEW) {
      setItemNumber(route.params?.BARCODENEW || '');
    }
  }, []);
  useEffect(() => {
    setInitialState({
      itemNumber,
      itemName,
      itemNameextra,
      cost,
      price,
      rentalPrice,
      inStock,
      department,
      tags,
      location,
      vendor,
      vendorpart,
      reoderquanity,
      reorderlevel,
      reordercost,
      unitsize,
      unitType,
      foodstampable,
      tax1,
      tax2,
      tax3,
      tax4,
      tax5,
      tax6,
      checkid1,
      checkid2,
      costper,
      casecost,
      numberincase,
      poQuanity,
      subcategory,
      brand,
    });
  }, [itemData]); // Reset when itemData changes
  const hasUnsavedChanges = () => {
    return (
      itemNumber !== initialState.itemNumber ||
      itemName !== initialState.itemName ||
      itemNameextra !== initialState.itemNameextra ||
      Number(cost) !== Number(initialState.cost) ||
      Number(price) !== Number(initialState.price) ||
      Number(rentalPrice) !== Number(initialState.rentalPrice) ||
      Number(inStock) !== Number(initialState.inStock) ||
      department !== initialState.department ||
      tags !== initialState.tags ||
      location !== initialState.location ||
      vendor !== initialState.vendor ||
      vendorpart !== initialState.vendorpart ||
      Number(reoderquanity) !== Number(initialState.reoderquanity) ||
      Number(reorderlevel) !== Number(initialState.reorderlevel) ||
      Number(reordercost) !== Number(initialState.reordercost) ||
      unitsize !== initialState.unitsize ||
      unitType !== initialState.unitType ||
      foodstampable !== initialState.foodstampable ||
      tax1 !== initialState.tax1 ||
      tax2 !== initialState.tax2 ||
      tax3 !== initialState.tax3 ||
      tax4 !== initialState.tax4 ||
      tax5 !== initialState.tax5 ||
      tax6 !== initialState.tax6 ||
      checkid1 !== initialState.checkid1 ||
      checkid2 !== initialState.checkid2 ||
      Number(costper) !== Number(initialState.costper) ||
      Number(casecost) !== Number(initialState.casecost) ||
      Number(numberincase) !== Number(initialState.numberincase) ||
      Number(poQuanity) !== Number(initialState.poQuanity) ||
      subcategory !== initialState.subcategory ||
      brand !== initialState.brand
    );
  };
  useEffect(() => {
    getALTSKU();
  }, [itemData[0]?.ItemNum]);

  const toggleLookup = useCallback((value: boolean) => {
    setshowLookup(value);

    if (Platform.OS === 'android') {
      if (value) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        setSearchQuery('');
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      setSearchQuery('');
      Keyboard.dismiss();
    }
  }, []);
  useFocusEffect(
    useCallback(() => {
      getVendorItemsDetails();
      getVendor();
      setTagAlong(false);
      setInserted(false);
      getInitDept();
      getSubCatogoryOrBrand();
      checkIsEdit();
      getTagAlongItems();

      setShowMore(route?.params?.isFromLottery || false);
    }, []),
  );

  const checkIsEdit = async () => {
    if (itemData[0]?.ItemName) {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/inventory/:ItemNum',
        {ItemNum: itemData[0]?.ItemNum},
      );

      setInStock(Number(getBarcode[0]?.In_Stock));
      setSuccess(true);
      const isEditClicked = await AsyncStorage.getItem('ISEDITCLICKED');
      if (isEditClicked) {
        setIsEdit(true);
      } else {
        setIsEdit(false);
      }
    } else {
      setIsEdit(true);
    }
  };
  useEffect(() => {
    getTagAlongItems();
    if (route.params?.NEWBARCODE) {
      setIsRestore(true);
    }
  }, [route.params?.NEWBARCODE]);

  useEffect(() => {
    getTagAlongItems();
  }, [route.params?.ISJUSTBACK]);
  const getTagAlongItems = async () => {
    const getBarcode = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: itemNumber},
    );

    if (Array.isArray(getBarcode) && getBarcode.length === 0) {
      const getUpdatedTagAlongs = await AsyncStorage.getItem('SetTagAlongs');
      console.log('CAN LOG 1', getUpdatedTagAlongs);

      if (getUpdatedTagAlongs) {
        const parsedTagAlongs: TagAlong[] = JSON.parse(getUpdatedTagAlongs);
        console.log('CAN LOG 2', getUpdatedTagAlongs);

        setTagAlongsItems(parsedTagAlongs);
      }
    } else {
      if (itemNumber) {
        GetItemsParamsNoFilter(
          (await getInventoryPort()).toString(),
          '/GetTagalong/:ItemNum',
          setTagAlongsItems,
          {
            ItemNum: itemNumber,
          },
        );

        GetItemsParamsNoFilter(
          (await getInventoryPort()).toString(),
          '/GetTagalong/:ItemNum',
          setTagAlongs,
          {
            ItemNum: itemData[0]?.ItemNum,
          },
        );
      }
    }
  };
  useEffect(() => {
    setNewSKU('');
  }, [AltSKUsData]);

  useEffect(() => {
    updateTagAlongs();
  }, [tagAlong]);

  useEffect(() => {
    if (
      selectedBrand ||
      selectedDepartment ||
      selectedVendor ||
      selectedSubCategory
    ) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  useEffect(() => {
    const getFilteredData = tagAlongs.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;
      return (
        matchesDepartment && matchesVendor && matchesBrand && matchesSubCategory
      );
    });
    setTagAlongsFilter(getFilteredData);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    tagAlongs,
  ]);

  const updateTagAlongs = async () => {
    const Tagged = route.params?.Tagged;
    if (Tagged) {
      // Check if the item already exists in tagAlongs
      const isItemExist = tagAlongs.some(
        item => item.TagAlong_ItemNum === Tagged.ItemNum,
      );

      if (isItemExist) {
      } else {
        // Create the pushTagAlong object
        const pushTagAlong = {
          ItemNum: itemNumber,
          Quantity: 1,
          Store_ID: '1001',
          TagAlong_ItemNum: Tagged?.ItemNum,
        };

        // Update the tagAlongs state by adding the new pushTagAlong object
        setTagAlongs(prevTagAlongs => [...prevTagAlongs, pushTagAlong]);
      }
    }
  };

  const getAlongItmes = async () => {
    if (itemData[0]?.ItemNum) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/GetTagalong/:ItemNum',
        setTagAlongs,
        {
          ItemNum: itemData[0]?.ItemNum,
        },
      );
    }
  };
  const getVendorItemsDetails = async () => {
    if (route.params?.VENDORITEM) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getvendoritemsbyItemNum/:Vendor_Number/:ItemNum',
        setInventoryVendor,
        {
          Vendor_Number: itemData[0]?.Vendor_Number,
          ItemNum: itemData[0]?.ItemNum,
        },
      );
    }
  };
  const getInitDept = async () => {
    GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setDepartments,
      setLoading,
    );

    GetAllItems<Unit_Types[]>(
      (await getLotteryPort()).toString(),
      '/GetAllUnits',
      setUnitTypes,
      setLoading,
    );
  };
  const getALTSKU = async () => {
    if (itemData[0]?.ItemNum || isRestore) {
      GetItemsParamsNoFilter<AltSKU[]>(
        (await getInventoryPort()).toString(),
        '/GetViewAltSKUS/:ItemNum',
        setAltSKUsData,
        {ItemNum: itemNumber},
      );

      GetItemsParamsNoFilter<AltSKU[]>(
        (await getInventoryPort()).toString(),
        '/GetViewAltSKUS/:ItemNum',
        setInitAltSKUsData,
        {ItemNum: itemNumber},
      );
      GetItemsParamsNoFilter<Activate_Book[]>(
        (await getLotteryPort()).toString(),
        '/GetAllHistoryDetails/:ItemNum',
        setBookHistory,
        {ItemNum: itemNumber},
      );
    }
  };

  const getVendor = async () => {
    GetAllItems<Vendor[]>(
      (await getInventoryPort()).toString(),
      '/GetVendors',
      setVendors,
      setLoading,
    );

    GetAllItems<BrandAdd[]>(
      (await getLotteryPort()).toString(),
      '/GetAllBrands',
      setBranding,
      setLoading,
    );
    console.log('BarcodeDetails');

    GetAllItems<SubCategoryAdd[]>(
      (await getLotteryPort()).toString(),
      '/GetAllSubCategories',
      setSubCat,
      setLoading,
    );
  };

  const getSubCatogoryOrBrand = async () => {
    // Fetch the data using your existing function
    const data = await GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getInventoryFilterBy_BrandOrCategory',
      SetBrandSubOrCat,
      SetBrandSubOrCat,
      setLoading,
    );

    const brandOnly = data
      .filter((item: {Brand: string | null}) => item.Brand !== null)
      .map((item: {Brand: string}) => ({Brand: item.Brand}));

    const subCategoriesOnly = data
      .filter((item: {SubCategory: string | null}) => item.SubCategory !== null)
      .map((item: {SubCategory: string}) => ({SubCategory: item.SubCategory}));

    // Set the transformed data into the state
    setGetBrands(brandOnly);
    setGetSubCategories(subCategoriesOnly);
  };

  const vendorOptions = vendors.map(vent => ({
    label: vent.Company,
    value: vent.Vendor_Number,
  }));

  const brandsOptions = branding.map(vent => ({
    label: vent.Brand,
    value: vent.Brand,
  }));
  const subCatOptions = subCat.map(vent => ({
    label: vent.SubCategory,
    value: vent.SubCategory,
  }));

  const unitTypesOptions = unitTypes.map(vent => ({
    label: vent.Unit_Type,
    value: vent.Unit_Type,
  }));

  const brandOptions = getBrands.map(vent => ({
    label: vent.Brand,
    value: vent.Brand,
  }));

  const subCategoryOptions = getSubCategories.map(vent => ({
    label: vent.SubCategory,
    value: vent.SubCategory,
  }));

  const departmentOptions = departments.map(dept => ({
    label: dept.Description,
    value: dept.Dept_ID,
  }));

  const tagsOptions = tagAlongsItems.map(dept => ({
    label: dept.TagAlong_ItemNum,
    value: dept.TagAlong_ItemNum,
  }));

  const CreateOrUpdateBarcode = async () => {
    const storeId = await AsyncStorage.getItem('STOREID');
    try {
      const inventoryData: Partial<Inventory> = {
        ItemNum: itemNumber,
        ItemName: itemName,
        Dept_ID: department,
        Cost: Number(cost),
        Price: Number(price),
        Retail_Price: Number(rentalPrice),
        In_Stock: Number(inStock),
        Date_Created: itemData[0]?.ItemNum
          ? itemData[0]?.Date_Created
          : getFormateDate(Date()),
        Last_Sold: itemData[0]?.ItemNum
          ? itemData[0]?.Last_Sold
          : getFormateDate(Date()),
        Location: location,
        Vendor_Number: vendor,
        Vendor_Part_Num: vendorpart,
        Reorder_Level: Number(reorderlevel),
        Reorder_Quantity: Number(reoderquanity),
        ReOrder_Cost: Number(reordercost),
        Unit_Size: Number(unitsize),
        Unit_Type: unitType,
        FoodStampable: foodstampable,
        Tax_1: route?.params?.isFromLottery ? false : tax1,
        Tax_2: tax2,
        Tax_3: tax3,
        Tax_4: tax4,
        Tax_5: tax5,
        Tax_6: tax6,
        Check_ID: checkid1,
        Check_ID2: checkid2,
        Store_ID: storeId === null ? '1001' : storeId,
        ItemName_Extra: itemNameextra,
      };
      const applyDefault = applyDefaults(inventoryData);

      if (!itemData[0]?.ItemName && !isRestore) {
        const result = await createData<Inventory>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/createbarcode',
        });
        if (result) {
          setItemDataFiler([applyDefault]);
          //setSuccess(true);
          if (!route.params?.ISPO) {
            showAlertMulti(
              'Your Item Has Been Added.',
              'Would You Like to Add Another Item?',
              'Duplicate Last',
            ).then(result => {
              if (result === 'Yes') {
                setItemNumber('');
                setItemName('');
                setItemNameextra('');
                setCost('');
                setPrice('');
                setRentalPrice('');
                setInStock(0);
                setDepartment('');
                setLocation('');
                setVendor('');
                setVendorPart('');
                setReoderQuanity('');
                setReorderLevel('');
                setReorderCost('');
                setUnitSize('');
                setUnitType('');
                setfoodstampable(false);
                setTax1(true);
                setTax2(false);
                setTax3(false);
                setTax4(false);
                setTax5(false);
                setTax6(false);
                setCheckid1(false);
                setCheckid2(false);
                setCostPer('');
                setCaseCost('');
                setNumberinCase('');
                setSubcategory('');
                setBrand('');
                setAltSKUsData([]);
                scrollViewRef.current?.scrollTo({y: 0, animated: true});
                textInputRef?.current?.focus();
              } else if (result === 'No') {
                if (route.params?.ISCREATE === true) {
                  navigation.navigate('Home', {screen: 'Scanner'});
                } else if (route.params?.isFromLottery) {
                  navigation.goBack();
                } else {
                  navigation.pop(2);
                }
              } else {
                setItemNumber('');
                setAltSKUsData([]);
                setLocation('');
                setVendorPart('');
                scrollViewRef.current?.scrollTo({y: 0, animated: true});
                textInputRef?.current?.focus();
              }
            });
          }
        } else {
        }
        setIsUpdate(true);
      } else {
        const result = await updateData<Inventory>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/updatebarcode',
        });
      }
    } catch (error) {
      console.log(error);
    }
  };
  const CreateOrUpdateVendor = async () => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    try {
      // Only proceed if vendor is selected and not empty
      if (!vendor || vendor.trim() === '') {
        console.log('No vendor selected, skipping vendor operations');
        return;
      }

      const inventoryData: Partial<InventoryVendor> = {
        ItemNum: itemNumber,
        Store_ID: ValidStore,
        Vendor_Number: vendor,
        Vendor_Part_Num: vendorpart,
        CostPer: Number(costper),
        Case_Cost: Number(casecost),
        NumPerVenCase: Number(numberincase),
      };

      const applyDefault = applyDefaultsInventoryVendor(inventoryData);

      if (isRestore || itemData[0]?.ItemName || isUpdate) {
        console.log('VENDOR OPERATION - Mode: Update/Restore', {
          isRestore,
          hasItemData: !!itemData[0]?.ItemName,
          isUpdate,
          vendor,
          itemNumber,
        });

        // Check if this specific vendor-item combination exists
        const vendorItems = await GetItemsParamsNoFilterNoReturn(
          (await getInventoryPort()).toString(),
          '/getvendoritemsbyItemNum/:Vendor_Number/:ItemNum',
          {Vendor_Number: vendor, ItemNum: itemNumber},
        );

        console.log('Vendor items check result:', {
          vendorItemsLength: Array.isArray(vendorItems)
            ? vendorItems.length
            : 'not array',
          vendorItems: vendorItems,
        });

        if (Array.isArray(vendorItems) && vendorItems.length === 0) {
          // This vendor-item combination doesn't exist, create it
          console.log('Creating new vendor-item relationship');
          const result = await createData<InventoryVendor>({
            baseURL: (await getInventoryPort()).toString(),
            data: applyDefault,
            endpoint: '/createinvedors',
          });
          if (result) {
            setInventoryVendor([applyDefault]);
          }
        } else {
          // This vendor-item combination exists, update it
          console.log('Updating existing vendor-item relationship');
          const vendorUpdateData: Partial<Inventory_Vendors> = {
            ItemNum: itemNumber,
            Store_ID: ValidStore,
            Vendor_Number: vendor,
            Vendor_Part_Num: vendorpart,
            CostPer: Number(costper),
            Case_Cost: Number(casecost),
            NumPerVenCase: Number(numberincase),
          };

          const applyUpdateDefaults =
            applyDefaultsInventoryVendorsUpdate(vendorUpdateData);

          const updateResult = await updateData<Inventory_Vendors>({
            baseURL: (await getInventoryPort()).toString(),
            data: applyUpdateDefaults,
            endpoint: '/updateinventoryvendors',
          });
          if (updateResult) {
            const vendorForState: InventoryVendor = {
              ItemNum: applyUpdateDefaults.ItemNum,
              Store_ID: applyUpdateDefaults.Store_ID,
              Vendor_Number: applyUpdateDefaults.Vendor_Number,
              Vendor_Part_Num: applyUpdateDefaults.Vendor_Part_Num,
              CostPer: applyUpdateDefaults.CostPer,
              Case_Cost: applyUpdateDefaults.Case_Cost,
              NumPerVenCase: applyUpdateDefaults.NumPerVenCase,
              CubeCost: applyUpdateDefaults.CubeCost,
              WeightCost: applyUpdateDefaults.WeightCost,
              OverrideCommission: applyUpdateDefaults.OverrideCommission,
              LandedCost: applyUpdateDefaults.LandedCost,
            };
            setInventoryVendor([vendorForState]);
          }
        }
      } else {
        if (!itemData[0]?.ItemName && !isRestore && !isUpdate) {
          console.log('VENDOR CREATED 22');

          const result = await createData<InventoryVendor>({
            baseURL: (await getInventoryPort()).toString(),
            data: applyDefault,
            endpoint: '/createinvedors',
          });
          if (result) {
            setInventoryVendor([applyDefault]);
          }
        } else {
          console.log('VENDOR UPDATED 22');

          // For updates in the else branch, use the update-specific type and function
          const vendorUpdateData: Partial<Inventory_Vendors> = {
            ItemNum: itemNumber,
            Store_ID: ValidStore,
            Vendor_Number: vendor,
            Vendor_Part_Num: vendorpart,
            CostPer: Number(costper),
            Case_Cost: Number(casecost),
            NumPerVenCase: Number(numberincase),
          };

          const applyUpdateDefaults =
            applyDefaultsInventoryVendorsUpdate(vendorUpdateData);

          const updateResult = await updateData<Inventory_Vendors>({
            baseURL: (await getInventoryPort()).toString(),
            data: applyUpdateDefaults,
            endpoint: '/updateinventoryvendors',
          });
          if (updateResult) {
            // Convert back to InventoryVendor format for state consistency
            const vendorForState: InventoryVendor = {
              ItemNum: applyUpdateDefaults.ItemNum,
              Store_ID: applyUpdateDefaults.Store_ID,
              Vendor_Number: applyUpdateDefaults.Vendor_Number,
              Vendor_Part_Num: applyUpdateDefaults.Vendor_Part_Num,
              CostPer: applyUpdateDefaults.CostPer,
              Case_Cost: applyUpdateDefaults.Case_Cost,
              NumPerVenCase: applyUpdateDefaults.NumPerVenCase,
              CubeCost: applyUpdateDefaults.CubeCost,
              WeightCost: applyUpdateDefaults.WeightCost,
              OverrideCommission: applyUpdateDefaults.OverrideCommission,
              LandedCost: applyUpdateDefaults.LandedCost,
            };
            setInventoryVendor([vendorForState]);
          }
        }
      }
    } catch (error) {
      console.log('CreateOrUpdateVendor error:', error);
    }
  };

  const CreateInventory_In = async () => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const CashierID = await AsyncStorage.getItem('SWIPEID');
    const ValideCashier = CashierID === null ? '100101' : CashierID;

    const inventoryAdjustData: Partial<Inventory_In> = {
      ItemNum: itemNumber,
      Store_ID: ValidStore,
      Quantity: inStock,
      DateTime: getFormateDate(Date()),
      Dirty: true,
      TransType: 'C',
      Description: 'ITEM CREATION',
      Cashier_ID: ValideCashier,
      CostPer: Number(cost),
    };
    const applyDefault = applyDefaultsInventoryAdjust(inventoryAdjustData);
    await createData<Inventory_In>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/createinvetoryin',
    });
  };

  const addAltSku = async (AltSku: string) => {
    setCamera(false);

    if (AltSku === '') {
      Alert.alert('Validation Error', 'Please enter a SKU');
      return;
    }

    const checkExists = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: AltSku},
    );

    if (Array.isArray(checkExists) && checkExists.length === 0) {
      const checkExistsSku = AltSKUsData.filter(item => item.AltSKU === AltSku);

      if (!checkExistsSku || checkExistsSku.length <= 0) {
        const storeId = await AsyncStorage.getItem('STOREID');
        const ValidStore = storeId === null ? '1001' : storeId;

        const newAltSkus: AltSKU = {
          AltSKU: AltSku,
          ItemNum: itemNumber,
          Store_ID: ValidStore,
        };

        setAltSKUsData(prevData => [...prevData, newAltSkus]);
      } else {
        Alert.alert('SKU Already Exists in This Item');
      }
    } else {
      Alert.alert('SKU Already Beinng Used By Another Item');
    }
  };
  const handleNavBack = async () => {
    if (hasUnsavedChanges()) {
      showAlert(
        `You have unsaved changes. Are you sure you want to discard them?`,
        'Confirmation',
        true,
        'Cancel',
        'Discard Changes',
      )
        .then(async result => {
          if (result) {
            await AsyncStorage.removeItem('SetTagAlongs');
            navigation.goBack();

            await AsyncStorage.removeItem('ISEDITCLICKED');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } else {
      await AsyncStorage.removeItem('SetTagAlongs');
      navigation.goBack();

      await AsyncStorage.removeItem('ISEDITCLICKED');
    }
  };

  const AddItems = async () => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;

    try {
      const checkCaseQty =
        numberincase === 0 || numberincase === ''
          ? Number(poQuanity)
          : Number(numberincase) * Number(poQuanity);
      const checkNumCase =
        numberincase === 0 || numberincase === '' ? 0 : numberincase;

      const TotalCost =
        Number(checkCaseQty) > 1
          ? Number(cost) * Number(checkCaseQty)
          : Number(cost);

      const ExpectedRecive = Number(
        numberincase === 0 || numberincase === '' ? poQuanity : checkCaseQty,
      );

      const cashPack =
        numberincase === 0 || numberincase === '' ? 0 : Number(poQuanity);

      const poItmes: Partial<PurchaseOrderItems> = {
        PO_Number: route.params?.MAINPO?.PO_Number,
        ItemNum: itemNumber,
        Quan_Ordered: checkCaseQty,
        CostPer: Number(cost),
        Quan_Received: 0,
        Vendor_Part_Number: vendorpart,
        CasePack: cashPack,
        Store_ID: ValidStore,
        destStore_ID: ValidStore,
        NumberPerCase: Number(checkNumCase),
      };

      const applyDefault = applyDefaultsPurchaseOrderItem(poItmes);

      setSuccess(true);
      const createResult = await createData<PurchaseOrderItems>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createpodetails',
      });

      if (createResult) {
        const calExistTotal =
          Number(route.params?.MAINPO?.Total_Cost) + Number(TotalCost);
        const calExistExpected =
          Number(route.params?.MAINPO?.ExpectedAmountToReceive) +
          Number(ExpectedRecive);

        updatePO(calExistTotal, calExistExpected);
      } else {
        console.log('Error');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updatePO = async (TotalCost?: number, Expected?: number) => {
    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: route.params?.MAINPO?.PO_Number,
      Total_Cost: TotalCost,
      Total_Cost_Received: route.params?.MAINPO?.Total_Cost_Received,
      Total_Charges: route.params?.MAINPO?.Total_Charges,
      ExpectedAmountToReceive: Expected,
      Store_ID: route.params?.MAINPO?.Store_ID,
      DateTime: route.params?.MAINPO?.DateTime,
      Reference: route.params?.MAINPO?.Reference,
      Vendor_Number: route.params?.MAINPO?.Vendor_Number,
      Ship_Via: route.params?.MAINPO?.Ship_Via,
      Status: route.params?.MAINPO?.Status,
      Cashier_ID: route.params?.MAINPO?.Cashier_ID,
      Due_Date: route.params?.MAINPO?.Due_Date,
      Last_Modified: route.params?.MAINPO?.Last_Modified,
      Cancel_Date: route.params?.MAINPO?.DateTime,
      Order_Reason: route.params?.MAINPO?.Order_Reason,
      POType: route.params?.MAINPO?.POType,
      Dirty: route.params?.MAINPO?.Dirty,
      Print_Notes_On_PO: route.params?.MAINPO?.Print_Notes_On_PO,
      ShipTo_1: route.params?.MAINPO?.ShipTo_1,
      ShipTo_2: route.params?.MAINPO?.ShipTo_2,
      ShipTo_4: route.params?.MAINPO?.ShipTo_4,
      Terms: route.params?.MAINPO?.Terms,
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
    if (result) {
      if (route.params?.ISPO) {
        navigation.navigate('PurchaseOrderItem', {
          ItemData: route.params?.MAINPO,
          POType: route?.params?.ItemData,
        });
      } else {
        navigation.navigate('ReturnToVendor', {
          ItemData: route.params?.MAINPO,
          POTYPE: route.params?.POTYPE,
        });
      }
    } else {
      console.log('Error!!');
    }
  };

  const DeleteTagAlongItems = (Barcode: string, TagAlong: string) => {
    showAlert('Are you sure you want to Delete?').then(async result => {
      if (result) {
        const updateQuery = tagAlongs.filter(
          item => item.TagAlong_ItemNum !== TagAlong,
        );

        if (!itemData[0]?.ItemName && !isRestore) {
          setTagAlongs(updateQuery);
        } else {
          try {
            const deleteResponse = await deleteItem(
              (await getInventoryPort()).toString(),
              '/DeleteTagAlone/:ItemNum/:TagAlong_ItemNum',
              {ItemNum: Barcode, TagAlong_ItemNum: TagAlong},
            );
            // If the deletion was successful
            if (deleteResponse?.success) {
              setTagAlongs(updateQuery);
            }
          } catch (error) {
            console.log('LOGGG ERROR', error);
          }
        }
      }
    });
  };

  const renderItem = ({item}: {item: TagAlong}) => {
    return (
      <View style={styles.tagAlongItem}>
        <Text style={styles.tagAlongText}>{item.TagAlong_ItemNum}</Text>
        <TouchableOpacity
          onPress={() =>
            DeleteTagAlongItems(item.ItemNum, item.TagAlong_ItemNum)
          }>
          <MaterialCommunityIcons
            name="delete"
            color={'tomato'}
            size={hp('4%')}
          />
        </TouchableOpacity>
      </View>
    );
  };

  const CreateOrupdateTagAlongs = async () => {
    console.log('ITEM TAG TESTE', itemData[0]?.ItemName, isUpdate);

    if (!itemData[0]?.ItemName || !isUpdate) {
      const getUpdatedTagAlongs = await AsyncStorage.getItem('SetTagAlongs');
      console.log('ITEM TAG TESTE 1', getUpdatedTagAlongs);
      if (getUpdatedTagAlongs) {
        const parsedTagAlongs: TagAlong[] = JSON.parse(getUpdatedTagAlongs);
        console.log('ITEM TAG TESTE 2', getUpdatedTagAlongs);
        const storeId = await AsyncStorage.getItem('STOREID');
        const ValidStore = storeId === null ? '1001' : storeId;
        for (const newValue of parsedTagAlongs) {
          const createTag: TagAlong = {
            ItemNum: itemNumber,
            Quantity: 1,
            Store_ID: ValidStore,
            TagAlong_ItemNum: newValue?.TagAlong_ItemNum,
          };

          const result = await createItem(
            (await getInventoryPort()).toString(),
            '/createtagalongs',
            createTag,
          );
        }
        await AsyncStorage.removeItem('SetTagAlongs');
      }
    }
  };

  const CreateOrUpdateAddition = async () => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const AdditionalInfo: Partial<AdditionalInfo> = {
      Store_ID: ValidStore,
      ItemNum: itemNumber,
      Brand: brand,
      SubCategory: subcategory,
    };

    const inventoryWithDefaults = applyDefaultsAddition(AdditionalInfo);

    try {
      if (itemData[0]?.ItemName || isRestore) {
        const additionalInfo = await GetItemsParamsNoFilterNoReturn(
          (await getInventoryPort()).toString(),
          '/getInventoryAdditional/:ItemNum',
          {ItemNum: itemNumber},
        );
        if (Array.isArray(additionalInfo) && additionalInfo.length === 0) {
          const result = await createItem(
            (await getInventoryPort()).toString(),
            '/createinventoryaddit',
            inventoryWithDefaults,
          );
        } else {
          const result = await updateData<AdditionalInfo>({
            baseURL: (await getInventoryPort()).toString(),
            data: inventoryWithDefaults,
            endpoint: '/updateinventoryadditionalinfo',
          });
        }
      } else {
        if (!itemData[0]?.ItemName && !isRestore) {
          const result = await createItem(
            (await getInventoryPort()).toString(),
            '/createinventoryaddit',
            inventoryWithDefaults,
          );
        } else {
          const result = await createItem(
            (await getInventoryPort()).toString(),
            '/createinventoryaddit',
            inventoryWithDefaults,
          );
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const CreateOrUpdateSKUS = async () => {
    if (itemData[0]?.ItemName || isRestore) {
      const results = await findDifferenceCommon(AltSKUsData, initAltSKUsData);
      // console.log(results, 'ISRESTORE');
      if (results.length > 0) {
        results.map(async newData => {
          const storeId = await AsyncStorage.getItem('STOREID');
          const ValidStore = storeId === null ? '1001' : storeId;
          const altSKUData: AltSKU = {
            Store_ID: ValidStore,
            ItemNum: itemNumber,
            AltSKU: newData.AltSKU,
          };

          const applyDefault = applyDefaultsAltSKU(altSKUData);
          const result = await createData<AltSKU>({
            baseURL: (await getInventoryPort()).toString(),
            data: applyDefault,
            endpoint: '/createaltskus',
          });

          if (result) {
            GetItemsParamsNoFilter<AltSKU[]>(
              (await getInventoryPort()).toString(),
              '/GetViewAltSKUS/:ItemNum',
              setAltSKUsData,
              {ItemNum: itemNumber},
            );

            GetItemsParamsNoFilter<AltSKU[]>(
              (await getInventoryPort()).toString(),
              '/GetViewAltSKUS/:ItemNum',
              setInitAltSKUsData,
              {ItemNum: itemNumber},
            );
          }
        });
      }
      navigation.goBack();
    } else {
      AltSKUsData.map(async sku => {
        const storeId = await AsyncStorage.getItem('STOREID');
        const ValidStore = storeId === null ? '1001' : storeId;
        const altSKUData: AltSKU = {
          Store_ID: ValidStore,
          ItemNum: itemNumber,
          AltSKU: sku.AltSKU,
        };

        const applyDefault = applyDefaultsAltSKU(altSKUData);
        const result = await createData<AltSKU>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/createaltskus',
        });

        if (result) {
          GetItemsParamsNoFilter<AltSKU[]>(
            (await getInventoryPort()).toString(),
            '/GetViewAltSKUS/:ItemNum',
            setAltSKUsData,
            {ItemNum: itemNumber},
          );

          GetItemsParamsNoFilter<AltSKU[]>(
            (await getInventoryPort()).toString(),
            '/GetViewAltSKUS/:ItemNum',
            setInitAltSKUsData,
            {ItemNum: itemNumber},
          );
        }
      });
    }
  };
  const removeSkus = async (RemoveSku: AltSKU) => {
    if (itemData[0]?.ItemName || isRestore) {
      showAlert('Are you sure you want to delete Sku?')
        .then(async result => {
          if (result) {
            const checkExists = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/getUnitAltSKU/:ItemNum/:AltSKU',
              {ItemNum: RemoveSku.ItemNum, AltSKU: RemoveSku.AltSKU},
            );

            if (Array.isArray(checkExists) && checkExists.length === 0) {
              const updatedArraySel = AltSKUsData.filter(
                item => item.AltSKU !== RemoveSku.AltSKU,
              );

              setAltSKUsData(updatedArraySel);
            } else {
              const result = await deleteItem(
                (await getInventoryPort()).toString(),
                '/DeleteAltSku/:ItemNum/:AltSKU',
                {ItemNum: itemNumber, AltSKU: RemoveSku.AltSKU},
              );

              if (result?.success) {
                const updatedArraySel = AltSKUsData.filter(
                  item => item.AltSKU !== RemoveSku.AltSKU,
                );

                setAltSKUsData(updatedArraySel);
              }
            }
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } else {
      showAlert('Are you sure you want to delete Sku?')
        .then(async result => {
          if (result) {
            const updateSkus = AltSKUsData.filter(
              item => item.AltSKU !== RemoveSku.AltSKU,
            );
            setAltSKUsData(updateSkus);
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    }
  };
  const renderItemSkus = ({item}: {item: AltSKU}) => (
    <View style={styles.altSkuItem}>
      <Text style={styles.altValue}>{item.AltSKU}</Text>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => removeSkus(item)}>
        <MaterialCommunityIcons
          name="delete-outline"
          color={MaterialColors.error.main}
          size={22}
        />
      </TouchableOpacity>
    </View>
  );
  const renderItemHistory = ({item}: {item: Activate_Book}) => (
    <View style={styles.bookHistoryItem}>
      <View style={{flexDirection: 'column'}}>
        <Text style={styles.bookNo}>{`Book No: ${item.Book_No}`}</Text>
        <Text
          style={styles.createdAt}>{`Book Tickets: ${item.Book_Tickets}`}</Text>
        <Text style={styles.createdAt}>{`Created At: ${
          new Date(item.Book_Created).toISOString().split('T')[0]
        }`}</Text>
      </View>

      <View style={{position: 'absolute', right: wp('4%'), top: wp('4%')}}>
        <Text style={styles.bookDetails}>{`Game ID: ${item.Game_ID}`}</Text>
      </View>
    </View>
  );
  const UpsertItmes = async () => {
    await AsyncStorage.removeItem('ISEDITCLICKED');
    Keyboard.dismiss();

    if (!route.params?.ISPO) {
      if (route.params?.isFromLottery) {
        if (
          itemNumber === '' ||
          itemName === '' ||
          department === '' ||
          price === '' ||
          location === '' ||
          vendorpart === '' ||
          inStock === ''
        ) {
          Alert.alert(
            'Please Enter Required Details!',
            '',
            [
              {
                text: 'OK',
                onPress: () => {
                  textInputRef?.current?.blur();
                  setTimeout(() => {
                    textInputRef?.current?.focus();
                  }, 1200);
                },
              },
            ],
            {cancelable: false},
          );
        } else {
          try {
            await CreateOrUpdateBarcode();
            await CreateOrUpdateVendor();
            await CreateOrupdateTagAlongs();
            await CreateOrUpdateAddition();
            await CreateOrUpdateSKUS();

            if (route.params?.isFromLottery) {
              const formattedSerial = formatNumber(openSerial);
              const splitSerial = formattedSerial.split('-');
              const bookData: Partial<Activate_Book> = {
                Game_ID: vendorpart,
                Book_No: lotteryBook,
                Book_Created: getFormateDate(Date()),
                Book_Tickets: Number(inStock),
                CreatedBy: 'Owner',
                Location: location,
                ItemNum: itemNumber,
                Open_Serial: splitSerial[2],
              };
              const applyDefault = applyDefaultsActivateBook(bookData);

              const gameDetails = await GetItemsParamsNoFilter(
                (await getLotteryPort()).toString(),
                '/GetShiftBookTickets/:ItemNum/:Location',
                setBookDetails,
                {ItemNum: itemNumber, Location: location},
              );

              if (Array.isArray(gameDetails) && gameDetails.length === 0) {
                const result = await createData<Activate_Book>({
                  baseURL: (await getLotteryPort()).toString(),
                  data: applyDefault,
                  endpoint: '/createactivatebook',
                });
                if (result) {
                  const result = await createData<Activate_Book>({
                    baseURL: (await getLotteryPort()).toString(),
                    data: applyDefault,
                    endpoint: '/createactivatedbookhistory',
                  });
                }
              } else {
                const result = await updateData<Activate_Book>({
                  baseURL: (await getLotteryPort()).toString(),
                  data: applyDefault,
                  endpoint: '/updateactivatebooks',
                });
                if (result) {
                  const result = await createData<Activate_Book>({
                    baseURL: (await getLotteryPort()).toString(),
                    data: applyDefault,
                    endpoint: '/createactivatedbookhistory',
                  });
                }
              }
            }
            if (!itemData[0]?.ItemName && !isRestore) {
              await CreateInventory_In();
              await createInventoryRef(
                (await getInventoryPort()).toString(),
                itemNumber,
              );
            }
          } catch (error) {
            console.log(error);
          }
        }
      } else {
        if (
          itemNumber === '' ||
          itemName === '' ||
          department === '' ||
          price === ''
        ) {
          Alert.alert(
            'Please Enter Required Details!',
            '',
            [
              {
                text: 'OK',
                onPress: () => {
                  textInputRef?.current?.blur();
                  setTimeout(() => {
                    textInputRef?.current?.focus();
                  }, 1200);
                },
              },
            ],
            {cancelable: false},
          );
        } else {
          try {
            console.log('LOG 1');

            await CreateOrUpdateBarcode();
            await CreateOrUpdateVendor();
            await CreateOrupdateTagAlongs();
            await CreateOrUpdateAddition();
            await CreateOrUpdateSKUS();
            if (Number(inStock) > 0 || inStock !== 0 || inStock) {
              await CreateInventory_In();
            }
            if (!itemData[0]?.ItemName && !isRestore) {
              await createInventoryRef(
                (await getInventoryPort()).toString(),
                itemNumber,
              );
            }
          } catch (error) {
            console.log(error);
          }
        }
      }
    } else {
      if (
        itemNumber === '' ||
        itemName === '' ||
        department === '' ||
        cost === '' ||
        poQuanity === ''
      ) {
        Alert.alert(
          'Please Enter Required Details!',
          '',
          [
            {
              text: 'OK',
              onPress: () => {
                textInputRefOrdering?.current?.blur();
                setTimeout(() => {
                  textInputRefOrdering?.current?.focus();
                }, 1200);
              },
            },
          ],
          {cancelable: false},
        );
      } else {
        try {
          await CreateOrUpdateBarcode();
          await CreateOrUpdateVendor();
          await CreateOrupdateTagAlongs();
          await CreateOrUpdateAddition();
          await CreateOrUpdateSKUS();
          if (route.params?.ISPO || route.params?.ISDSD) {
            await AddItems();
          }

          if (!route.params?.ISDSD && !success) {
            Alert.alert('Item Saved!');
          }
          if (!itemData[0]?.ItemName && !isRestore) {
            await CreateInventory_In();
            await createInventoryRef(
              (await getInventoryPort()).toString(),
              itemNumber,
            );
          }
        } catch (error) {
          console.log(error);
        }
      }
    }
  };
  const CheckDepartment = async () => {
    const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');
    if (lotteryDepartment && department && lotteryDepartment === department) {
      setIsSameDepartment(true);
    } else {
      setIsSameDepartment(false);
    }
  };
  useEffect(() => {
    CheckDepartment();
  }, [department]);

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    handleSearch(
      text,
      tagAlongs,
      ['TagAlong_ItemNum'],
      setTagAlongsFilter,
      setLoading,
    );
  };

  const checkItemExist = async (text: string) => {
    try {
      if (text) {
        const getBarcode = await GetItemsParamsNoFilterNoReturn(
          (await getInventoryPort()).toString(),
          '/inventory/:ItemNum',
          {ItemNum: text},
        );

        if (Array.isArray(getBarcode) && getBarcode.length === 1) {
          if (getBarcode[0]?.IsDeleted) {
            showAlert(
              `This Item Number is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName} Would You Like to Restore it?`,
              'Confirmation',
              true,
              'NO',
              'YES',
            )
              .then(async result => {
                const vendorItems = await GetItemsParamsNoFilterNoReturn(
                  (await getInventoryPort()).toString(),
                  '/getvendoritemsbyItemNum/:Vendor_Number/:ItemNum',
                  {Vendor_Number: getBarcode[0]?.Vendor_Number, ItemNum: text},
                );

                const inventoryAdditional = await GetItemsParamsNoFilter(
                  (await getInventoryPort()).toString(),
                  '/getInventoryAdditional/:ItemNum',
                  setInventoryAdditional,
                  {
                    ItemNum: text,
                  },
                  false,
                );

                if (result) {
                  setItemName(text);
                  setItemName(getBarcode[0]?.ItemName);
                  setItemNameextra(getBarcode[0]?.ItemName_Extra);
                  setCost(getBarcode[0]?.Cost);
                  setPrice(getBarcode[0]?.Price);
                  setRentalPrice(getBarcode[0]?.Retail_Price);
                  setInStock(getBarcode[0]?.In_Stock);
                  setDepartment(getBarcode[0]?.Dept_ID);
                  setLocation(getBarcode[0]?.Location);
                  setVendor(getBarcode[0]?.Vendor_Number);
                  setVendorPart(getBarcode[0]?.Vendor_Part_Num);
                  setReoderQuanity(getBarcode[0]?.Reorder_Quantity);
                  setReorderLevel(getBarcode[0]?.Reorder_Level);
                  setReorderCost(getBarcode[0]?.ReOrder_Cost);
                  setUnitSize(getBarcode[0]?.Unit_Size);
                  setUnitType(getBarcode[0]?.Unit_Type);
                  setfoodstampable(getBarcode[0]?.FoodStampable);
                  setTax1(getBarcode[0]?.Tax_1[0]);
                  setTax2(getBarcode[0]?.Tax_2[0]);
                  setTax3(getBarcode[0]?.Tax_3[0]);
                  setTax4(getBarcode[0]?.Tax_4[0]);
                  setTax5(getBarcode[0]?.Tax_5[0]);
                  setTax6(getBarcode[0]?.Tax_6[0]);
                  setCheckid1(getBarcode[0]?.Check_ID[0]);
                  setCheckid2(getBarcode[0]?.Check_ID2[0]);
                  setCostPer(vendorItems ? vendorItems[0]?.CostPer : '');
                  setCaseCost(vendorItems ? vendorItems[0]?.Case_Cost : '');
                  setNumberinCase(
                    vendorItems ? vendorItems[0]?.NumPerVenCase : '',
                  );
                  setSubcategory(inventoryAdditional[0]?.SubCategory);
                  setBrand(inventoryAdditional[0]?.Brand);

                  GetItemsParamsNoFilter<AltSKU[]>(
                    (await getInventoryPort()).toString(),
                    '/GetViewAltSKUS/:ItemNum',
                    setAltSKUsData,
                    {ItemNum: text},
                  );

                  GetItemsParamsNoFilter<AltSKU[]>(
                    (await getInventoryPort()).toString(),
                    '/GetViewAltSKUS/:ItemNum',
                    setInitAltSKUsData,
                    {ItemNum: text},
                  );
                  GetItemsParamsNoFilter(
                    (await getInventoryPort()).toString(),
                    '/GetTagalong/:ItemNum',
                    setTagAlongsItems,
                    {
                      ItemNum: text,
                    },
                  );
                  setIsRestore(true);
                  setIsUpdate(true);
                  setItemData([getBarcode[0]]);
                  setIsEdit(true);
                }
              })
              .catch(error => {
                console.error('Error showing alert', error);
              });
          } else {
            showAlert(
              `This Item Number is Already in Use Another Department. By an Item With a Discription: ${getBarcode[0]?.ItemName} Would You Like to Edit it?`,
              'Confirmation',
              true,
              'NO',
              'YES',
            )
              .then(async result => {
                const vendorItems = await GetItemsParamsNoFilterNoReturn(
                  (await getInventoryPort()).toString(),
                  '/getvendoritemsbyItemNum/:Vendor_Number/:ItemNum',
                  {Vendor_Number: getBarcode[0]?.Vendor_Number, ItemNum: text},
                );

                const inventoryAdditional = await GetItemsParamsNoFilter(
                  (await getInventoryPort()).toString(),
                  '/getInventoryAdditional/:ItemNum',
                  setInventoryAdditional,
                  {
                    ItemNum: text,
                  },
                  false,
                );

                if (result) {
                  setItemName(text);
                  setItemName(getBarcode[0]?.ItemName);
                  setItemNameextra(getBarcode[0]?.ItemName_Extra);
                  setCost(getBarcode[0]?.Cost);
                  setPrice(
                    route.params?.LOTTERYDEPT ? '' : getBarcode[0]?.Price,
                  );
                  setRentalPrice(getBarcode[0]?.Retail_Price);
                  setInStock(
                    route.params?.LOTTERYDEPT ? '' : getBarcode[0]?.In_Stock,
                  );
                  setDepartment(
                    route.params?.LOTTERYDEPT || getBarcode[0]?.Dept_ID || '',
                  );
                  setLocation(
                    route.params?.LOTTERYDEPT ? '' : getBarcode[0]?.Location,
                  );
                  setVendor(
                    route.params?.LOTTERYDEPT
                      ? ''
                      : getBarcode[0]?.Vendor_Number,
                  );
                  setVendorPart(
                    route.params?.LOTTERYDEPT
                      ? ''
                      : getBarcode[0]?.Vendor_Part_Num,
                  );
                  setReoderQuanity(getBarcode[0]?.Reorder_Quantity);
                  setReorderLevel(getBarcode[0]?.Reorder_Level);
                  setReorderCost(getBarcode[0]?.ReOrder_Cost);
                  setUnitSize(getBarcode[0]?.Unit_Size);
                  setUnitType(getBarcode[0]?.Unit_Type);
                  setfoodstampable(getBarcode[0]?.FoodStampable);
                  setTax1(getBarcode[0]?.Tax_1[0]);
                  setTax2(getBarcode[0]?.Tax_2[0]);
                  setTax3(getBarcode[0]?.Tax_3[0]);
                  setTax4(getBarcode[0]?.Tax_4[0]);
                  setTax5(getBarcode[0]?.Tax_5[0]);
                  setTax6(getBarcode[0]?.Tax_6[0]);
                  setCheckid1(getBarcode[0]?.Check_ID[0]);
                  setCheckid2(getBarcode[0]?.Check_ID2[0]);
                  setCostPer(vendorItems ? vendorItems[0]?.CostPer : '');
                  setCaseCost(vendorItems ? vendorItems[0]?.Case_Cost : '');
                  setNumberinCase(
                    vendorItems ? vendorItems[0]?.NumPerVenCase : '',
                  );
                  setSubcategory(inventoryAdditional[0]?.SubCategory);
                  setBrand(inventoryAdditional[0]?.Brand);

                  GetItemsParamsNoFilter<AltSKU[]>(
                    (await getInventoryPort()).toString(),
                    '/GetViewAltSKUS/:ItemNum',
                    setAltSKUsData,
                    {ItemNum: text},
                  );

                  GetItemsParamsNoFilter<AltSKU[]>(
                    (await getInventoryPort()).toString(),
                    '/GetViewAltSKUS/:ItemNum',
                    setInitAltSKUsData,
                    {ItemNum: text},
                  );
                  GetItemsParamsNoFilter(
                    (await getInventoryPort()).toString(),
                    '/GetTagalong/:ItemNum',
                    setTagAlongsItems,
                    {
                      ItemNum: text,
                    },
                  );
                  setIsRestore(true);
                  setIsUpdate(true);
                  setItemData([getBarcode[0]]);
                  setIsEdit(true);
                }
              })
              .catch(error => {
                console.error('Error showing alert', error);
              });
          }
        }
      }
    } catch (error) {
      console.error('Error showing alert', error);
    }
  };

  const checkLocationExist = async (text: string) => {
    if (text) {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/getLocationExist/:Dept_ID/:Location',
        {Dept_ID: department, Location: text},
      );

      if (Array.isArray(getBarcode) && getBarcode.length > 0) {
        showAlertOK(
          `This Item Location is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName}`,
          'Already in Use',
        );
      }
    }
  };

  useEffect(() => {
    if (hook) {
      getTagAlongItems();
    }
  }, [hook]);

  const [camera, setCamera] = useState(false);

  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        addAltSku(codes[0].value);
      }
    },
  });
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    rootContainer: {
      backgroundColor: colors.background,
      paddingHorizontal: 8,
      flex: 1,
      justifyContent: 'space-between',
    },
    contentContainer: {
      paddingHorizontal: wp('2.5%'),
    },
    barcodeRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    barcodeInput: {
      flex: 1,
    },
    buttonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginVertical: hp('1.5%'),
      gap: wp('2%'),
    },
    rightAlignContainer: {
      flex: 1,
      alignItems: 'flex-end',
    },
    actionButton: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingVertical: hp('1.2%'),
      paddingHorizontal: wp('3%'),
      elevation: 2,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 1.5,
      flex: 1,
      maxWidth: '48%',
    },
    actionButtonContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    actionButtonIcon: {
      marginRight: 6,
    },
    actionButtonText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: '#FFFFFF',
      textAlign: 'center',
    },
    bottomButtonContainer: {
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
      justifyContent: 'center',
      paddingBottom: hp('0.5%'),
    },
    modalBackdrop: {
      backgroundColor: 'rgba(0,0,0,0.5)',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      backgroundColor: colors.card,
      width: wp('93%'),
      height: hp('80%'),
      borderRadius: 15,
      paddingVertical: 10,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('2.5%'),
      marginVertical: hp('1.5%'),
    },
    modalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2.5%'),
      color: colors.text,
    },
    modalBottomInput: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      left: 0,
      paddingVertical: 10,
      paddingHorizontal: wp('2.5%'),
    },
    tagAlongModalContainer: {
      backgroundColor: colors.background,
      width: '100%',
      height: '100%',
      borderRadius: 15,
      paddingVertical: 10,
    },
    searchContainer: {
      paddingHorizontal: wp('2.5%'),
      marginVertical: hp('1.5%'),
    },
    tagAlongButtonContainer: {
      paddingHorizontal: 10,
      paddingVertical: 10,
      position: 'absolute',
      bottom: 0,
      right: 0,
      left: 0,
    },
    filterModalContainer: {
      backgroundColor: colors.card,
      width: '93%',
      height: '75%',
      borderRadius: 15,
      paddingVertical: 10,
    },
    filterHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
    },
    filterTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2.5%'),
      color: colors.text,
    },
    filterContent: {
      paddingHorizontal: wp('2.5%'),
      marginTop: 20,
    },
    clearFilterButton: {
      marginHorizontal: wp('2.5%'),
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1%'),
      alignItems: 'center',
      justifyContent: 'center',
    },
    clearFilterText: {
      fontFamily: Fonts.OnestMedium,
      textAlign: 'center',
      fontSize: hp('2%'),
      color: colors.primary,
    },
    tagAlongItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
      backgroundColor: colors.surface,
      marginHorizontal: wp('2.5%'),
      marginVertical: hp('0.5%'),
      borderRadius: 10,
      borderWidth: 1,
      borderColor: colors.border,
    },
    tagAlongText: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2%'),
      color: colors.text,
    },
    altSkuItem: {
      backgroundColor: colors.surface,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
      marginHorizontal: wp('2.5%'),
      marginBottom: 10,
      borderRadius: 15,
      borderWidth: 1,
      borderColor: colors.border,
    },
    altValue: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
    },
    deleteButton: {
      // padding: 8,
    },
    bookHistoryItem: {
      backgroundColor: colors.surface,
      padding: wp('4%'),
      marginHorizontal: wp('2.5%'),
      marginBottom: 10,
      borderRadius: 15,
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 5,
      elevation: 3,
    },
    bookNo: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2.3%'),
      color: colors.primary,
      marginBottom: 5,
    },
    bookDetails: {
      fontFamily: Fonts.OnestRegular,
      fontSize: hp('2%'),
      color: colors.textSecondary,
      textAlign: 'right',
    },
    createdAt: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2%'),
      color: colors.textSecondary,
      textAlign: 'left',
    },
    showMoreButton: {
      backgroundColor: colors.primary,
      borderRadius: 24,
      paddingVertical: hp('1.2%'),
      alignSelf: 'center',
      justifyContent: 'center',
      width: wp('25%'),
      marginBottom: hp('8%'),
      elevation: 3,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.3 : 0.15,
      shadowRadius: 3,
    },
    showMoreButtonContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    showMoreButtonText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: '#FFFFFF',
      textAlign: 'center',
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    skuModalContainer: {
      backgroundColor: colors.card,
      width: wp('93%'),
      height: hp('80%'),
      borderRadius: 15,
      paddingVertical: 10,
    },
    skuModalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('2.5%'),
      marginVertical: hp('1.5%'),
    },
    skuModalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
    },
    closeButton: {
      paddingLeft: 10,
    },
    skuListContainer: {
      flex: 1,
    },
    skuInputContainer: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      left: 0,
      paddingVertical: 10,
      paddingHorizontal: wp('2.5%'),
    },
  });

  return (
    <View style={styles.rootContainer}>
      <ScrollView ref={scrollViewRef}>
        <View style={styles.contentContainer}>
          <Header
            NavName={
              itemData[0]?.ItemName || success
                ? 'Update Item'
                : 'Create New Item'
            }
            Onpress={handleNavBack}
            isProvid
          />

          <View style={styles.barcodeRow}>
            <View style={styles.barcodeInput}>
              <AppFocus
                Title="Barcode"
                PlaceHolder="Scan Item Number"
                onChangeText={text => {
                  setItemNumber(text);
                  if (!showLookup) {
                    checkItemExist(text);
                  }
                }}
                Value={itemNumber}
                AutoFocus={itemData[0]?.ItemName ? false : true}
                isBarcode={true}
                isRequired={true}
                Editable={!itemData[0]?.ItemName ? true : false}
                textInputRef={textInputRef}
                keyboardON={showLookup}
                onToggleLookup={value => toggleLookup(value)}
                onBlur={
                  showLookup ? () => checkItemExist(itemNumber) : undefined
                }
                IsAdd={true}
                IsAddPress={() => (isEdit ? setModalVisible(true) : () => {})}
              />
            </View>
          </View>
          <AppTextInput
            PlaceHolder="Enter Item Name"
            Title="Item Name"
            Value={itemName}
            onChangeText={text => setItemName(text)}
            isRequired={true}
            Editable={isEdit ? true : false}
            maxLength={30}
          />
          {!route?.params?.isFromLottery && (
            <>
              <AppTextInput
                PlaceHolder="Enter Item 2nd Name"
                Title="2nd Name"
                Value={itemNameextra}
                onChangeText={text => setItemNameextra(text)}
                Editable={isEdit ? true : false}
              />
            </>
          )}

          <AppDropDown
            label="Department"
            options={departmentOptions}
            selectedValue={department}
            onSelect={value => setDepartment(value)}
            isRequired={true}
            disabled={isEdit ? false : true}
          />

          {!route?.params?.isFromLottery && (
            <>
              <AppDropDown
                label="Brand"
                options={brandsOptions}
                selectedValue={brand}
                onSelect={value => {
                  setBrand(value);
                }}
                isAdd={true}
                onCreate={() =>
                  isEdit ? navigation.navigate('Brands') : () => {}
                }
                disabled={isEdit ? false : true}
              />

              <AppDropDown
                label="SubCategory"
                options={subCatOptions}
                selectedValue={subcategory}
                onSelect={value => {
                  setSubcategory(value);
                }}
                isAdd={true}
                onCreate={() =>
                  isEdit ? navigation.navigate('SubCategory') : () => {}
                }
                disabled={isEdit ? false : true}
              />

              <AppTextInput
                PlaceHolder="Enter Cost"
                Title="Cost"
                Value={cost}
                onChangeText={text => setCost(Number(text))}
                isNumeric
                isRequired={route.params?.ISPO && true}
                Editable={itemData[0]?.ItemName ? false : true}
              />

              <AppDropDown
                options={tagsOptions}
                selectedValue={tags}
                onSelect={value => setTags(value)}
                IsNotEdit={true}
                isAdd={true}
                IsDelete={true}
                IsCreate={
                  itemData[0]?.ItemName || isUpdate || isRestore ? false : true
                }
                IsTagAlong={true}
                ItemNumber={itemNumber}
                onChangeHookCall={() => setHook(true)}
                onCreate={() =>
                  isEdit
                    ? navigation.navigate('TagAlongs', {
                        ISRESTOREITEM: isRestore ? true : false,
                        ItemData: itemNumber,
                        IsCreate:
                          itemData[0]?.ItemName || isUpdate || isRestore
                            ? false
                            : true,
                      })
                    : () => {}
                }
                isNotClear={true}
                disabled={isEdit ? false : true}
              />
            </>
          )}

          <AppTextInput
            PlaceHolder="Enter Price"
            Title="Price"
            Value={price}
            onChangeText={text => setPrice(Number(text))}
            isNumeric
            isRequired={!route.params?.ISPO && true}
            Editable={isEdit ? true : false}
          />
          {!route?.params?.isFromLottery && (
            <>
              <AppTextInput
                PlaceHolder="Enter Retail Price"
                Title="Retail Price"
                Value={rentalPrice}
                onChangeText={text => setRentalPrice(Number(text))}
                isNumeric
                Editable={isEdit ? true : false}
              />
            </>
          )}

          <AppTextInput
            PlaceHolder={
              !route.params?.ISPO ? 'Enter In Stock' : 'Enter Ordering Quanity'
            }
            Title={
              !route.params?.ISPO
                ? route.params?.LOTTERYDEPT
                  ? 'Qty.Of Tickets'
                  : route?.params?.isFromLottery
                  ? 'Qty.Of Tickets'
                  : 'In Stock'
                : 'Ordering Quanity'
            }
            IsAdd={
              itemData[0]?.ItemName
                ? route?.params?.isFromLottery
                  ? false
                  : true
                : false
            }
            IsAddPress={async () => {
              const isAuthorized = await hasPermission('CFA_HH_Inv_Adjust');

              if (!isAuthorized) {
                Alert.alert('You do not have permission to adjust stock.');
                return;
              }
              isEdit
                ? navigation.navigate('AdjustStock', {
                    StockData: itemData,
                  })
                : () => {};
            }}
            Value={route?.params?.ISPO ? poQuanity : inStock}
            onChangeText={text =>
              route?.params?.ISPO
                ? setPoQuanity(Number(text))
                : setInStock(Number(text))
            }
            Editable={
              itemData[0]?.ItemName
                ? route.params?.isFromLottery
                  ? isEdit
                    ? true
                    : false
                  : false
                : true
            }
            isNumeric
            isRequired={
              route.params?.ISPO || (route.params?.isFromLottery && true)
            }
            textInputRef={route.params?.ISPO && textInputRefOrdering}
          />

          {showMore && (
            <View>
              <AppTextInput
                PlaceHolder="Enter Location"
                Title="Location"
                Value={location}
                onChangeText={text => setLocation(text)}
                Editable={isEdit ? true : false}
                isRequired={route.params?.isFromLottery && true}
                maxLength={20}
                onBlur={() => {
                  route.params?.isFromLottery
                    ? checkLocationExist(location)
                    : undefined;
                }}
              />

              {!route?.params?.isFromLottery && (
                <>
                  <AppDropDown
                    label="Vendors"
                    options={vendorOptions}
                    selectedValue={vendor}
                    onSelect={value => {
                      setVendor(value);
                    }}
                    isAdd={true}
                    onCreate={async () => {
                      const isAuthorized = await hasPermission(
                        'CFA_Vendors_Add',
                      );

                      if (!isAuthorized) {
                        Alert.alert(
                          'You do not have permission to add vendors.',
                        );
                        return;
                      }
                      navigation.navigate('Vendors');
                    }}
                    disabled={isEdit ? false : true}
                  />
                </>
              )}
              <AppTextInput
                PlaceHolder={
                  route?.params?.isFromLottery
                    ? 'Scan Game Number'
                    : 'Enter Vendor Part'
                }
                Title={
                  route?.params?.isFromLottery
                    ? 'Scan Game Number'
                    : 'Part Number'
                }
                Value={vendorpart}
                onChangeText={text => {
                  if (route.params?.isFromLottery) {
                    const sixDigits = text.slice(0).slice(0, 3);
                    const sixDigitsBook = text.slice(0).slice(4, 10);
                    const sixDigitsBookSerial = text.slice(0).slice(10, 13);

                    if (Number(sixDigitsBookSerial) != Number(inStock) - 1) {
                      // Clear the input field immediately
                      setVendorPart('');
                      setOpenSerial('');
                      // Also clear the input field using ref
                      if (textInputRefGameNumber.current) {
                        textInputRefGameNumber.current.clear();
                      }
                      console.log(vendorpart, 'VEN PART 1111');
                      // Use setTimeout to ensure state update happens before alert
                      setTimeout(() => {
                        Alert.alert('Error, Please Check the Ticket QTY!');
                      }, 100);
                      console.log(vendorpart, 'VEN PART 2222');

                      return;
                    } else {
                      setOpenSerial(text);
                      setVendorPart(sixDigits);
                      setLotteryBook(sixDigitsBook);
                    }
                  } else {
                    setVendorPart(text);
                  }
                }}
                Editable={isEdit ? true : false}
                isRequired={route.params?.isFromLottery && true}
                textInputRef={
                  route.params?.isFromLottery
                    ? textInputRefGameNumber
                    : undefined
                }
              />

              {!route?.params?.isFromLottery && (
                <>
                  <AppTextInput
                    PlaceHolder="Enter Re Order Quanity"
                    Title="ReOrder Quanity"
                    Value={reoderquanity}
                    onChangeText={text => setReoderQuanity(Number(text))}
                    Editable={isEdit ? true : false}
                  />

                  <AppTextInput
                    PlaceHolder="Enter ReOrder Level"
                    Title="ReOrder Level"
                    Value={reorderlevel}
                    onChangeText={text => setReorderLevel(Number(text))}
                    Editable={isEdit ? true : false}
                  />

                  <AppTextInput
                    PlaceHolder="Enter ReOrder Cost"
                    Title="ReOrder Cost"
                    Value={reordercost}
                    onChangeText={text => setReorderCost(Number(text))}
                    Editable={isEdit ? true : false}
                  />

                  <AppTextInput
                    PlaceHolder="Enter Cost Per"
                    Title="Cost Per"
                    Value={costper}
                    onChangeText={text => setCostPer(Number(text))}
                    Editable={isEdit ? true : false}
                  />

                  <AppTextInput
                    PlaceHolder="Enter Case Cost"
                    Title="Case Cost"
                    Value={casecost}
                    onChangeText={text => setCaseCost(Number(text))}
                    Editable={isEdit ? true : false}
                  />

                  <AppTextInput
                    PlaceHolder="Enter Number in Case"
                    Title="Number in Case"
                    Value={numberincase}
                    onChangeText={text => setNumberinCase(Number(text))}
                    Editable={isEdit ? true : false}
                  />

                  <AppTextInput
                    PlaceHolder="Enter Unit Size"
                    Title="Unit Size"
                    Value={unitsize}
                    onChangeText={text => setUnitSize(Number(text))}
                    Editable={isEdit ? true : false}
                  />

                  <AppDropDown
                    label="Unit Type"
                    options={unitTypesOptions}
                    selectedValue={unitType}
                    onSelect={value => setUnitType(value)}
                    isAdd={true}
                    onCreate={() =>
                      isEdit ? navigation.navigate('UnitType') : () => {}
                    }
                    disabled={isEdit ? false : true}
                  />

                  <BarocdeSwitch
                    name="Food Stampable"
                    value={foodstampable}
                    onValueChange={value => setfoodstampable(value)}
                    Editable={isEdit ? true : false}
                  />
                  <BarocdeSwitch
                    name="Tax 1"
                    value={tax1}
                    onValueChange={value => setTax1(value)}
                    Editable={isEdit ? true : false}
                  />
                  <BarocdeSwitch
                    name="Tax 2"
                    value={tax2}
                    onValueChange={value => setTax2(value)}
                    Editable={isEdit ? true : false}
                  />
                  <BarocdeSwitch
                    name="Tax 3"
                    value={tax3}
                    onValueChange={value => setTax3(value)}
                    Editable={isEdit ? true : false}
                  />
                  <BarocdeSwitch
                    name="Tax 4"
                    value={tax4}
                    onValueChange={value => setTax4(value)}
                    Editable={isEdit ? true : false}
                  />
                  <BarocdeSwitch
                    name="Tax 5"
                    value={tax5}
                    onValueChange={value => setTax5(value)}
                    Editable={isEdit ? true : false}
                  />
                  <BarocdeSwitch
                    name="Tax 6"
                    value={tax6}
                    onValueChange={value => setTax6(value)}
                    Editable={isEdit ? true : false}
                  />
                  <BarocdeSwitch
                    name="Check ID 1"
                    value={checkid1}
                    onValueChange={value => setCheckid1(value)}
                    Editable={isEdit}
                  />
                  <BarocdeSwitch
                    name="Check ID 2"
                    value={checkid2}
                    onValueChange={value => setCheckid2(value)}
                    Editable={isEdit ? true : false}
                  />
                </>
              )}
            </View>
          )}

          <View style={styles.buttonRow}>
            {!isSameDepartment ? (
              <>
                {/* <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => (isEdit ? setModalVisible(true) : () => {})}>
                  <View style={styles.actionButtonContent}>
                    <MaterialIcons
                      name="list-alt"
                      size={18}
                      color={MaterialColors.text.onDark}
                      style={styles.actionButtonIcon}
                    />
                    <Text style={styles.actionButtonText}>ALT SKUs</Text>
                  </View>
                </TouchableOpacity> */}

                {/* {(itemData[0]?.ItemName || success) && (
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={async () => {
                      const isAuthorized = await hasPermission(
                        'CFA_HH_Inv_Adjust',
                      );

                      if (!isAuthorized) {
                        Alert.alert(
                          'You do not have permission to adjust stock.',
                        );
                        return;
                      }
                      isEdit
                        ? navigation.navigate('AdjustStock', {
                            StockData: itemData,
                          })
                        : () => {};
                    }}>
                    <View style={styles.actionButtonContent}>
                      <MaterialIcons
                        name="edit"
                        size={18}
                        color={MaterialColors.text.onDark}
                        style={styles.actionButtonIcon}
                      />
                      <Text style={styles.actionButtonText}>Adjust Stock</Text>
                    </View>
                  </TouchableOpacity>
                )} */}
              </>
            ) : (
              <View style={styles.rightAlignContainer}>
                {(itemData[0]?.ItemName || success) && (
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => {
                      setModalVisibleHistory(true);
                    }}>
                    <View style={styles.actionButtonContent}>
                      <MaterialIcons
                        name="history"
                        size={18}
                        color={MaterialColors.text.onDark}
                        style={styles.actionButtonIcon}
                      />
                      <Text style={styles.actionButtonText}>Book History</Text>
                    </View>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>

          {!route?.params?.isFromLottery && (
            <>
              <TouchableOpacity
                style={styles.showMoreButton}
                onPress={() => setShowMore(!showMore)}>
                <View style={styles.showMoreButtonContent}>
                  <Text style={styles.showMoreButtonText}>
                    {showMore ? 'Less' : 'More'}
                  </Text>
                  <MaterialIcons
                    name={
                      showMore ? 'keyboard-arrow-up' : 'keyboard-arrow-down'
                    }
                    size={18}
                    color={MaterialColors.text.onDark}
                    style={{marginLeft: 8}}
                  />
                </View>
              </TouchableOpacity>
            </>
          )}
        </View>
      </ScrollView>

      {!itemData[0]?.ItemName && !isUpdate ? (
        <View style={styles.bottomButtonContainer}>
          <FAB
            label={'Save'}
            position="bottomRight"
            onPress={() => UpsertItmes()}
          />
        </View>
      ) : (
        <View style={styles.bottomButtonContainer}>
          {(route.params?.CANEDIT || isUpdate) && (
            <FAB
              label={isEdit ? 'Save & Close' : 'Edit'}
              position="bottomRight"
              onPress={async () => {
                if (isEdit) {
                  UpsertItmes();
                } else {
                  setIsEdit(true);
                  await AsyncStorage.setItem('ISEDITCLICKED', 'YES');
                }
              }}
            />
          )}
        </View>
      )}

      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalBackdrop}>
          <View style={styles.skuModalContainer}>
            <View style={styles.skuModalHeader}>
              <Text style={styles.skuModalTitle}>ALT SKUs</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}>
                <Ionicons
                  name="close-circle-sharp"
                  color={MaterialColors.error.main}
                  size={24}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.skuListContainer}>
              <DataList
                data={AltSKUsData}
                renderItem={renderItemSkus}
                loading={loading}
              />
            </View>

            <View
              style={{
                paddingVertical: 10,
                paddingHorizontal: 10,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '85%',
                  gap: 10,
                }}>
                <AppTextInput
                  PlaceHolder="Enter New SKU"
                  Title="New SKU"
                  Value={newSKU}
                  onChangeText={text => setNewSKU(text)}
                />
                <TouchableOpacity
                  style={{
                    backgroundColor: Primary,
                    padding: 7,
                    marginTop: 8,
                    borderRadius: 8,
                  }}>
                  <MaterialCommunityIcons
                    onPress={() => setCamera(!camera)}
                    name="camera"
                    size={23}
                    color={MaterialColors.text.onDark}
                  />
                </TouchableOpacity>
              </View>
              <AppButton Title={'Add SKU'} OnPress={() => addAltSku(newSKU)} />
            </View>
          </View>
        </View>
      </Modal>

      <Modal
        animationType="slide"
        transparent={true}
        visible={tagAlong}
        onRequestClose={() => setTagAlong(false)}>
        <View style={styles.modalBackdrop}>
          <View style={styles.tagAlongModalContainer}>
            <View style={styles.contentContainer}>
              <Header
                NavName="Tag Along Items"
                isProvid={true}
                Onpress={() => setTagAlong(false)}
              />
            </View>

            <View>
              <View style={styles.searchContainer}>
                <Search
                  value={searchQuery}
                  PlaceHolder="Search"
                  onChange={onSearchChange}
                  AutoFocus={true}
                />
              </View>
              <DataList
                data={tagAlongsFilter}
                renderItem={renderItem}
                loading={loading}
              />
            </View>

            <View style={styles.tagAlongButtonContainer}>
              <AppButton
                Title="Add Tag Along Items"
                OnPress={() =>
                  navigation.navigate('AddTagAlong', {
                    ItemData: itemNumber,
                  })
                }
              />
            </View>
          </View>
        </View>
      </Modal>

      <Modal
        animationType="slide"
        transparent={true}
        visible={filter}
        onRequestClose={() => setFilter(false)}>
        <View style={styles.modalBackdrop}>
          <View style={styles.filterModalContainer}>
            <View style={styles.filterHeader}>
              <Text style={styles.filterTitle}>Filter Items</Text>

              <TouchableOpacity
                style={{paddingLeft: 10}}
                onPress={() => setFilter(false)}>
                <AntDesign
                  name="closecircle"
                  color={'tomato'}
                  size={hp('4%')}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.filterContent}>
              <AppDropDown
                label="Department"
                options={departmentOptions}
                selectedValue={selectedDepartment}
                onSelect={value => {
                  setIsEnableFilter(true);
                  setSelectedDepartment(value);
                }}
              />

              <AppDropDown
                label="Vendors"
                options={vendorOptions}
                selectedValue={selectedVendor}
                onSelect={value => {
                  setIsEnableFilter(true);
                  setSelectedVendor(value);
                }}
              />

              <AppDropDown
                label="Brand"
                options={brandOptions}
                selectedValue={selectedBrand}
                onSelect={value => {
                  setIsEnableFilter(true);
                  setSelectedBrand(value);
                }}
              />

              <AppDropDown
                label="SubCategory"
                options={subCategoryOptions}
                selectedValue={selectedSubCategory}
                onSelect={value => {
                  setIsEnableFilter(true);
                  setSelectedSubCategory(value);
                }}
              />
            </View>

            {isEnableFilter && (
              <TouchableOpacity
                style={styles.clearFilterButton}
                onPress={() => {
                  setSelectedDepartment('');
                  setSelectedVendor('');
                  setSelectedBrand('');
                  setSelectedSubCategory('');
                }}>
                <Text style={styles.clearFilterText}>Clear All Filter</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Modal>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisibleHistory}
        onRequestClose={() => setModalVisibleHistory(false)}>
        <View style={styles.modalBackdrop}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Book History</Text>
              <TouchableOpacity onPress={() => setModalVisibleHistory(false)}>
                <Ionicons
                  name="close-circle-sharp"
                  color={'tomato'}
                  size={hp('5%')}
                />
              </TouchableOpacity>
            </View>

            <DataList
              data={BookHistory}
              renderItem={renderItemHistory}
              loading={loading}
            />
          </View>
        </View>
      </Modal>

      <Modal
        animationType="fade"
        transparent={false}
        visible={camera}
        onRequestClose={() => setCamera(false)}>
        <AppScanner
          codeScanner={codeScanner}
          onClose={() => {
            setCamera(false);
          }}
        />
      </Modal>
    </View>
  );
};

export default BarcodeDetails;
